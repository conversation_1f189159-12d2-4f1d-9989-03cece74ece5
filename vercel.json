{"buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "framework": "nextjs", "outputDirectory": ".next", "regions": ["fra1"], "functions": {"src/pages/api/ocr.ts": {"maxDuration": 30}}, "env": {"NEXT_PUBLIC_SUPABASE_URL": "https://jobktdnksbweojxfqvcs.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpvYmt0ZG5rc2J3ZW9qeGZxdmNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4MzI0MTEsImV4cCI6MjA2MzQwODQxMX0.2SK8lqqmHMlaUhzKQMn_sbyVSx9ibOrPBb8Irk3UmEA"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}