/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Removed swcMinify as it's now the default in Next.js 15+
  serverExternalPackages: ['tesseract.js'], // Moved from experimental.serverComponentsExternalPackages
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }
    return config;
  },
  // Removed invalid 'api' configuration - this should be handled in individual API routes
}

module.exports = nextConfig
